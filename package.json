{"name": "finance-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"dev": "expo start", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "reset": "expo start --clear"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/config-plugins": "^10.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-voice/voice": "^3.2.4", "@supabase/supabase-js": "^2.39.3", "base-64": "^1.0.0", "expo": "~53.0.0", "expo-auth-session": "~6.2.1", "expo-camera": "~16.1.11", "expo-constants": "~17.1.7", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.525.0", "md5": "^2.3.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.9", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/base-64": "^1.0.2", "@types/md5": "^2.3.5", "@types/react": "~19.0.10", "typescript": "^5.3.0"}, "private": true}