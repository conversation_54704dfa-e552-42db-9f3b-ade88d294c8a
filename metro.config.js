const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Ensure proper resolution for React Native and Expo Router
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Add support for additional asset extensions if needed
config.resolver.assetExts.push('db', 'mp3', 'ttf', 'obj', 'png', 'jpg');

// Ensure source extensions include tsx files
config.resolver.sourceExts.push('tsx', 'ts', 'jsx', 'js', 'json');

module.exports = config;